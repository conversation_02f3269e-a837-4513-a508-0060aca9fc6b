import React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { DocumentCardPropsI } from "./types";

const DocumentCard: React.FC<DocumentCardPropsI> = ({
  document,
  isSelected = false,
  onSelect,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const getDocumentIcon = (type: string) => {
    switch (type) {
      case "word":
        return (
          <div className="w-16 h-20 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
            <svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="32" height="32" rx="4" fill="#2B579A" />
              <path d="M8 8h16v16H8V8z" fill="white" fillOpacity="0.2" />
              <text
                x="16"
                y="20"
                textAnchor="middle"
                fill="white"
                fontSize="10"
                fontWeight="bold"
              >
                W
              </text>
            </svg>
          </div>
        );
      case "pdf":
        return (
          <div className="w-16 h-20 bg-red-100 rounded-lg flex items-center justify-center mb-3">
            <svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="32" height="32" rx="4" fill="#DC2626" />
              <path d="M8 8h16v16H8V8z" fill="white" fillOpacity="0.2" />
              <text
                x="16"
                y="20"
                textAnchor="middle"
                fill="white"
                fontSize="8"
                fontWeight="bold"
              >
                PDF
              </text>
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-16 h-20 bg-gray-100 rounded-lg flex items-center justify-center mb-3">
            <svg
              width="32"
              height="32"
              viewBox="0 0 32 32"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="32" height="32" rx="4" fill="#6B7280" />
              <path d="M8 8h16v16H8V8z" fill="white" fillOpacity="0.2" />
            </svg>
          </div>
        );
    }
  };

  const handleClick = () => {
    if (onSelect) {
      onSelect(document);
    } else {
      // Navigate to document viewer with current folder preserved
      const currentFolder = searchParams.get("folder");
      const documentUrl = currentFolder
        ? `/library/documents/${document.id}?folder=${encodeURIComponent(currentFolder)}`
        : `/library/documents/${document.id}`;
      router.push(documentUrl);
    }
  };

  return (
    <div
      className={`flex flex-col items-center p-4 rounded-lg border transition-all cursor-pointer ${
        isSelected
          ? "bg-primary-50 border-primary-200 shadow-sm"
          : "bg-white border-neutral-200 hover:border-neutral-300 hover:shadow-sm"
      }`}
      onClick={handleClick}
    >
      {getDocumentIcon(document.type)}
      <span
        className={`text-sm font-medium text-center ${
          isSelected ? "text-primary-700" : "text-neutral-900"
        }`}
      >
        {document.name}
      </span>
    </div>
  );
};

export default DocumentCard;

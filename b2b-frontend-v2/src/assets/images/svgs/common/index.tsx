import React from "react";

export const NavicaterAiIcon = () => (
  <svg
    width="23"
    height="23"
    viewBox="0 0 23 23"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M17.523 12.0981L12.6874 10.3112L10.9062 5.47184C10.8007 5.18528 10.6098 4.93796 10.3594 4.76327C10.1089 4.58857 9.81089 4.49491 9.50553 4.49491C9.20017 4.49491 8.90215 4.58857 8.65169 4.76327C8.40124 4.93796 8.21039 5.18528 8.10491 5.47184L6.31241 10.3112L1.47303 12.0925C1.18646 12.198 0.939149 12.3888 0.764454 12.6393C0.58976 12.8897 0.496094 13.1877 0.496094 13.4931C0.496094 13.7985 0.58976 14.0965 0.764454 14.3469C0.939149 14.5974 1.18646 14.7882 1.47303 14.8937L6.31241 16.6862L8.09366 21.5256C8.19914 21.8122 8.38999 22.0595 8.64044 22.2342C8.8909 22.4089 9.18892 22.5025 9.49428 22.5025C9.79964 22.5025 10.0977 22.4089 10.3481 22.2342C10.5986 22.0595 10.7894 21.8122 10.8949 21.5256L12.6874 16.6862L17.5268 14.905C17.8133 14.7995 18.0607 14.6086 18.2354 14.3582C18.41 14.1077 18.5037 13.8097 18.5037 13.5043C18.5037 13.199 18.41 12.901 18.2354 12.6505C18.0607 12.4 17.8133 12.2092 17.5268 12.1037L17.523 12.0981ZM11.8437 15.3943C11.7419 15.4319 11.6494 15.491 11.5727 15.5677C11.496 15.6445 11.4368 15.7369 11.3993 15.8387L9.49991 20.9847L7.60428 15.8425C7.56682 15.7396 7.50729 15.6462 7.42988 15.5687C7.35246 15.4913 7.25903 15.4318 7.15616 15.3943L2.01397 13.4987L7.15616 11.6031C7.25903 11.5656 7.35246 11.5061 7.42988 11.4287C7.50729 11.3513 7.56682 11.2578 7.60428 11.155L9.49991 6.01278L11.3955 11.155C11.4331 11.2568 11.4922 11.3492 11.5689 11.4259C11.6456 11.5027 11.7381 11.5618 11.8399 11.5993L16.9858 13.4987L11.8437 15.3943ZM12.4999 3.74872C12.4999 3.54981 12.5789 3.35904 12.7196 3.21839C12.8602 3.07774 13.051 2.99872 13.2499 2.99872H14.7499V1.49872C14.7499 1.29981 14.8289 1.10904 14.9696 0.968388C15.1102 0.827736 15.301 0.748718 15.4999 0.748718C15.6988 0.748718 15.8896 0.827736 16.0302 0.968388C16.1709 1.10904 16.2499 1.29981 16.2499 1.49872V2.99872H17.7499C17.9488 2.99872 18.1396 3.07774 18.2802 3.21839C18.4209 3.35904 18.4999 3.54981 18.4999 3.74872C18.4999 3.94763 18.4209 4.1384 18.2802 4.27905C18.1396 4.4197 17.9488 4.49872 17.7499 4.49872H16.2499V5.99872C16.2499 6.19763 16.1709 6.3884 16.0302 6.52905C15.8896 6.6697 15.6988 6.74872 15.4999 6.74872C15.301 6.74872 15.1102 6.6697 14.9696 6.52905C14.8289 6.3884 14.7499 6.19763 14.7499 5.99872V4.49872H13.2499C13.051 4.49872 12.8602 4.4197 12.7196 4.27905C12.5789 4.1384 12.4999 3.94763 12.4999 3.74872ZM22.2499 8.24872C22.2499 8.44763 22.1709 8.6384 22.0302 8.77905C21.8896 8.9197 21.6988 8.99872 21.4999 8.99872H20.7499V9.74872C20.7499 9.94763 20.6709 10.1384 20.5302 10.279C20.3896 10.4197 20.1988 10.4987 19.9999 10.4987C19.801 10.4987 19.6102 10.4197 19.4696 10.279C19.3289 10.1384 19.2499 9.94763 19.2499 9.74872V8.99872H18.4999C18.301 8.99872 18.1102 8.9197 17.9696 8.77905C17.8289 8.6384 17.7499 8.44763 17.7499 8.24872C17.7499 8.04981 17.8289 7.85904 17.9696 7.71839C18.1102 7.57774 18.301 7.49872 18.4999 7.49872H19.2499V6.74872C19.2499 6.54981 19.3289 6.35904 19.4696 6.21839C19.6102 6.07774 19.801 5.99872 19.9999 5.99872C20.1988 5.99872 20.3896 6.07774 20.5302 6.21839C20.6709 6.35904 20.7499 6.54981 20.7499 6.74872V7.49872H21.4999C21.6988 7.49872 21.8896 7.57774 22.0302 7.71839C22.1709 7.85904 22.2499 8.04981 22.2499 8.24872Z" />
  </svg>
);

export const NavicaterAiIconActive = () => (
  <svg
    width="23"
    height="23"
    viewBox="0 0 23 23"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M18.5 13.5C18.5019 13.8058 18.409 14.1047 18.2341 14.3555C18.0591 14.6063 17.8107 14.7968 17.5231 14.9006L12.6875 16.6875L10.9062 21.5269C10.8008 21.8134 10.6099 22.0608 10.3595 22.2355C10.109 22.4101 9.81099 22.5038 9.50562 22.5038C9.20026 22.5038 8.90224 22.4101 8.65178 22.2355C8.40133 22.0608 8.21048 21.8134 8.105 21.5269L6.3125 16.6875L1.47312 14.9062C1.18656 14.8008 0.93924 14.6099 0.764546 14.3595C0.589851 14.109 0.496185 13.811 0.496185 13.5056C0.496185 13.2003 0.589851 12.9022 0.764546 12.6518C0.93924 12.4013 1.18656 12.2105 1.47312 12.105L6.3125 10.3125L8.09375 5.47312C8.19923 5.18656 8.39008 4.93924 8.64053 4.76455C8.89099 4.58985 9.18901 4.49619 9.49437 4.49619C9.79974 4.49619 10.0978 4.58985 10.3482 4.76455C10.5987 4.93924 10.7895 5.18656 10.895 5.47312L12.6875 10.3125L17.5269 12.0938C17.8147 12.1986 18.0629 12.3901 18.2372 12.642C18.4115 12.8939 18.5033 13.1937 18.5 13.5ZM13.25 4.5H14.75V6C14.75 6.19891 14.829 6.38968 14.9697 6.53033C15.1103 6.67098 15.3011 6.75 15.5 6.75C15.6989 6.75 15.8897 6.67098 16.0303 6.53033C16.171 6.38968 16.25 6.19891 16.25 6V4.5H17.75C17.9489 4.5 18.1397 4.42098 18.2803 4.28033C18.421 4.13968 18.5 3.94891 18.5 3.75C18.5 3.55109 18.421 3.36032 18.2803 3.21967C18.1397 3.07902 17.9489 3 17.75 3H16.25V1.5C16.25 1.30109 16.171 1.11032 16.0303 0.96967C15.8897 0.829018 15.6989 0.75 15.5 0.75C15.3011 0.75 15.1103 0.829018 14.9697 0.96967C14.829 1.11032 14.75 1.30109 14.75 1.5V3H13.25C13.0511 3 12.8603 3.07902 12.7197 3.21967C12.579 3.36032 12.5 3.55109 12.5 3.75C12.5 3.94891 12.579 4.13968 12.7197 4.28033C12.8603 4.42098 13.0511 4.5 13.25 4.5ZM21.5 7.5H20.75V6.75C20.75 6.55109 20.671 6.36032 20.5303 6.21967C20.3897 6.07902 20.1989 6 20 6C19.8011 6 19.6103 6.07902 19.4697 6.21967C19.329 6.36032 19.25 6.55109 19.25 6.75V7.5H18.5C18.3011 7.5 18.1103 7.57902 17.9697 7.71967C17.829 7.86032 17.75 8.05109 17.75 8.25C17.75 8.44891 17.829 8.63968 17.9697 8.78033C18.1103 8.92098 18.3011 9 18.5 9H19.25V9.75C19.25 9.94891 19.329 10.1397 19.4697 10.2803C19.6103 10.421 19.8011 10.5 20 10.5C20.1989 10.5 20.3897 10.421 20.5303 10.2803C20.671 10.1397 20.75 9.94891 20.75 9.75V9H21.5C21.6989 9 21.8897 8.92098 22.0303 8.78033C22.171 8.63968 22.25 8.44891 22.25 8.25C22.25 8.05109 22.171 7.86032 22.0303 7.71967C21.8897 7.57902 21.6989 7.5 21.5 7.5Z" />
  </svg>
);

export const LibraryIcon = () => (
  <svg
    width="22"
    height="17"
    viewBox="0 0 22 17"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M20.9688 6.37122C20.8295 6.17823 20.6464 6.02112 20.4345 5.91288C20.2226 5.80464 19.988 5.74837 19.75 5.74872H18.25V4.24872C18.25 3.85089 18.092 3.46936 17.8107 3.18806C17.5294 2.90675 17.1478 2.74872 16.75 2.74872H10.2503L7.65063 0.798718C7.39054 0.604827 7.07503 0.499656 6.75062 0.498718H1.75C1.35218 0.498718 0.970644 0.656753 0.68934 0.938058C0.408035 1.21936 0.25 1.60089 0.25 1.99872V15.4987C0.25 15.6976 0.329018 15.8884 0.46967 16.029C0.610322 16.1697 0.801088 16.2487 1 16.2487H17.7906C17.948 16.2487 18.1015 16.1992 18.2292 16.1072C18.3569 16.0151 18.4524 15.8852 18.5022 15.7359L21.1731 7.72309C21.2482 7.49763 21.2689 7.25758 21.2334 7.0226C21.1978 6.78763 21.1072 6.5644 20.9688 6.37122ZM6.75062 1.99872L9.55 4.09872C9.67982 4.19608 9.83772 4.24872 10 4.24872H16.75V5.74872H4.54094C4.2261 5.74869 3.91924 5.84774 3.66382 6.03181C3.4084 6.21589 3.21738 6.47567 3.11781 6.77434L1.75 10.8768V1.99872H6.75062ZM17.2506 14.7487H2.04062L4.54094 7.24872H19.75L17.2506 14.7487Z" />
  </svg>
);
export const LibraryIconActive = () => (
  <svg
    width="22"
    height="17"
    viewBox="0 0 22 17"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M20.9688 6.3725C20.8295 6.17951 20.6464 6.0224 20.4345 5.91416C20.2226 5.80592 19.988 5.74965 19.75 5.75H18.25V4.25C18.25 3.85218 18.092 3.47064 17.8107 3.18934C17.5294 2.90804 17.1478 2.75 16.75 2.75H10.2503L7.65063 0.8C7.39054 0.606109 7.07503 0.500938 6.75062 0.5H1.75C1.35218 0.5 0.970644 0.658035 0.68934 0.93934C0.408035 1.22064 0.25 1.60218 0.25 2V15.5C0.25 15.6989 0.329018 15.8897 0.46967 16.0303C0.610322 16.171 0.801088 16.25 1 16.25H17.7906C17.948 16.25 18.1015 16.2005 18.2292 16.1085C18.3569 16.0164 18.4524 15.8865 18.5022 15.7372L21.1731 7.72437C21.2482 7.49891 21.2689 7.25886 21.2334 7.02388C21.1978 6.78891 21.1072 6.56569 20.9688 6.3725ZM6.75062 2L9.55 4.1C9.67982 4.19737 9.83772 4.25 10 4.25H16.75V5.75H4.54094C4.2261 5.74998 3.91924 5.84902 3.66382 6.03309C3.4084 6.21717 3.21738 6.47695 3.11781 6.77563L1.75 10.8781V2H6.75062Z" />
  </svg>
);
export const AdminPanelIcon = () => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M19.3984 4.91281L18.9616 4.66063C19.0128 4.38927 19.0128 4.11073 18.9616 3.83938L19.3984 3.58719C19.5707 3.48773 19.6965 3.3239 19.748 3.13173C19.7995 2.93957 19.7726 2.73481 19.6731 2.5625C19.5737 2.39019 19.4098 2.26445 19.2177 2.21294C19.0255 2.16142 18.8207 2.18836 18.6484 2.28781L18.2106 2.54094C18.0011 2.36118 17.7602 2.22165 17.5 2.12938V1.625C17.5 1.42609 17.421 1.23532 17.2803 1.09467C17.1397 0.954018 16.9489 0.875 16.75 0.875C16.5511 0.875 16.3603 0.954018 16.2197 1.09467C16.079 1.23532 16 1.42609 16 1.625V2.12938C15.7398 2.22165 15.4989 2.36118 15.2894 2.54094L14.8516 2.28781C14.7662 2.23857 14.6721 2.20661 14.5744 2.19376C14.4767 2.18091 14.3775 2.18743 14.2823 2.21294C14.1872 2.23844 14.098 2.28244 14.0198 2.34242C13.9417 2.4024 13.8761 2.47718 13.8269 2.5625C13.7776 2.64782 13.7457 2.742 13.7328 2.83967C13.72 2.93734 13.7265 3.03658 13.752 3.13173C13.7775 3.22688 13.8215 3.31608 13.8815 3.39423C13.9415 3.47237 14.0162 3.53794 14.1016 3.58719L14.5384 3.83938C14.4872 4.11073 14.4872 4.38927 14.5384 4.66063L14.1016 4.91281C13.9585 4.99533 13.8467 5.12275 13.7834 5.27532C13.7202 5.42789 13.7091 5.59706 13.7518 5.75659C13.7946 5.91612 13.8888 6.05708 14.0198 6.1576C14.1509 6.25811 14.3114 6.31256 14.4766 6.3125C14.6082 6.31291 14.7377 6.27829 14.8516 6.21219L15.2894 5.95906C15.4989 6.13882 15.7398 6.27835 16 6.37063V6.875C16 7.07391 16.079 7.26468 16.2197 7.40533C16.3603 7.54598 16.5511 7.625 16.75 7.625C16.9489 7.625 17.1397 7.54598 17.2803 7.40533C17.421 7.26468 17.5 7.07391 17.5 6.875V6.37063C17.7602 6.27835 18.0011 6.13882 18.2106 5.95906L18.6484 6.21219C18.7623 6.27829 18.8918 6.31291 19.0234 6.3125C19.1886 6.31256 19.3491 6.25811 19.4802 6.1576C19.6112 6.05708 19.7054 5.91612 19.7482 5.75659C19.7909 5.59706 19.7798 5.42789 19.7166 5.27532C19.6533 5.12275 19.5415 4.99533 19.3984 4.91281ZM16 4.25C16 4.10166 16.044 3.95666 16.1264 3.83332C16.2088 3.70999 16.3259 3.61386 16.463 3.55709C16.6 3.50032 16.7508 3.48547 16.8963 3.51441C17.0418 3.54335 17.1754 3.61478 17.2803 3.71967C17.3852 3.82456 17.4566 3.9582 17.4856 4.10368C17.5145 4.24917 17.4997 4.39997 17.4429 4.53701C17.3861 4.67406 17.29 4.79119 17.1667 4.8736C17.0433 4.95601 16.8983 5 16.75 5C16.5511 5 16.3603 4.92098 16.2197 4.78033C16.079 4.63968 16 4.44891 16 4.25ZM18.7516 8.76031C18.5554 8.79314 18.3803 8.90255 18.2648 9.06448C18.1493 9.2264 18.1028 9.42757 18.1356 9.62375C18.2118 10.0785 18.25 10.5389 18.25 11C18.2518 13.0196 17.5095 14.969 16.165 16.4759C15.3285 15.2638 14.1524 14.3261 12.7844 13.7806C13.5192 13.2019 14.0554 12.4085 14.3184 11.5108C14.5815 10.6132 14.5582 9.6559 14.2519 8.77207C13.9457 7.88825 13.3716 7.12183 12.6096 6.5794C11.8475 6.03696 10.9354 5.74548 10 5.74548C9.06462 5.74548 8.15248 6.03696 7.39043 6.5794C6.62839 7.12183 6.05432 7.88825 5.74805 8.77207C5.44178 9.6559 5.41855 10.6132 5.68157 11.5108C5.94459 12.4085 6.4808 13.2019 7.21563 13.7806C5.84764 14.3261 4.67147 15.2638 3.835 16.4759C2.78005 15.2872 2.09094 13.8189 1.85054 12.2479C1.61014 10.6768 1.82868 9.06966 2.47988 7.61981C3.13108 6.16996 4.18722 4.93908 5.5213 4.07516C6.85539 3.21125 8.41062 2.75108 10 2.75C10.4611 2.74993 10.9215 2.78818 11.3763 2.86438C11.5715 2.89519 11.7711 2.84764 11.9315 2.73208C12.0919 2.61652 12.2002 2.44229 12.2329 2.2473C12.2655 2.0523 12.2198 1.85231 12.1057 1.69082C11.9917 1.52934 11.8184 1.41943 11.6238 1.385C9.58698 1.04236 7.49401 1.35588 5.64703 2.28029C3.80006 3.2047 2.29465 4.69217 1.34817 6.52793C0.401694 8.3637 0.0631151 10.4528 0.381323 12.4935C0.699532 14.5342 1.65806 16.421 3.11851 17.8815C4.57897 19.3419 6.46577 20.3005 8.50651 20.6187C10.5472 20.9369 12.6363 20.5983 14.4721 19.6518C16.3078 18.7053 17.7953 17.1999 18.7197 15.353C19.6441 13.506 19.9576 11.413 19.615 9.37625C19.5822 9.18008 19.4728 9.00498 19.3108 8.88947C19.1489 8.77396 18.9477 8.7275 18.7516 8.76031ZM7 10.25C7 9.65666 7.17595 9.07664 7.50559 8.58329C7.83524 8.08994 8.30377 7.70542 8.85195 7.47836C9.40013 7.2513 10.0033 7.19189 10.5853 7.30764C11.1672 7.4234 11.7018 7.70912 12.1213 8.12868C12.5409 8.54824 12.8266 9.08279 12.9424 9.66473C13.0581 10.2467 12.9987 10.8499 12.7716 11.3981C12.5446 11.9462 12.1601 12.4148 11.6667 12.7444C11.1734 13.0741 10.5933 13.25 10 13.25C9.20435 13.25 8.44129 12.9339 7.87868 12.3713C7.31607 11.8087 7 11.0457 7 10.25ZM4.945 17.5156C5.48757 16.6671 6.23501 15.9688 7.11843 15.4851C8.00185 15.0013 8.99282 14.7478 10 14.7478C11.0072 14.7478 11.9982 15.0013 12.8816 15.4851C13.765 15.9688 14.5124 16.6671 15.055 17.5156C13.6097 18.6397 11.831 19.2499 10 19.2499C8.16904 19.2499 6.39031 18.6397 4.945 17.5156Z" />
  </svg>
);
export const AdminPanelIconActive = () => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M19.3983 4.91153L18.9615 4.65934C19.0127 4.38799 19.0127 4.10944 18.9615 3.83809L19.3983 3.58591C19.5706 3.48645 19.6964 3.32262 19.7479 3.13045C19.7994 2.93829 19.7725 2.73353 19.673 2.56122C19.5736 2.38891 19.4097 2.26317 19.2176 2.21166C19.0254 2.16014 18.8206 2.18707 18.6483 2.28653L18.2105 2.53966C18.001 2.3599 17.7601 2.22037 17.4999 2.12809V1.62372C17.4999 1.42481 17.4209 1.23404 17.2802 1.09339C17.1396 0.952736 16.9488 0.873718 16.7499 0.873718C16.551 0.873718 16.3602 0.952736 16.2196 1.09339C16.0789 1.23404 15.9999 1.42481 15.9999 1.62372V2.12809C15.7397 2.22037 15.4988 2.3599 15.2893 2.53966L14.8515 2.28653C14.7661 2.23729 14.672 2.20533 14.5743 2.19248C14.4766 2.17963 14.3774 2.18615 14.2822 2.21166C14.1871 2.23716 14.0979 2.28116 14.0197 2.34114C13.9416 2.40112 13.876 2.4759 13.8268 2.56122C13.7775 2.64654 13.7456 2.74072 13.7327 2.83839C13.7199 2.93606 13.7264 3.0353 13.7519 3.13045C13.7774 3.2256 13.8214 3.3148 13.8814 3.39295C13.9414 3.47109 14.0161 3.53666 14.1015 3.58591L14.5383 3.83809C14.4871 4.10944 14.4871 4.38799 14.5383 4.65934L14.1015 4.91153C13.9584 4.99404 13.8466 5.12147 13.7833 5.27404C13.7201 5.42661 13.709 5.59578 13.7517 5.75531C13.7945 5.91484 13.8887 6.0558 14.0197 6.15632C14.1508 6.25683 14.3113 6.31128 14.4765 6.31122C14.6081 6.31163 14.7376 6.27701 14.8515 6.21091L15.2893 5.95778C15.4988 6.13754 15.7397 6.27707 15.9999 6.36934V6.87372C15.9999 7.07263 16.0789 7.2634 16.2196 7.40405C16.3602 7.5447 16.551 7.62372 16.7499 7.62372C16.9488 7.62372 17.1396 7.5447 17.2802 7.40405C17.4209 7.2634 17.4999 7.07263 17.4999 6.87372V6.36934C17.7601 6.27707 18.001 6.13754 18.2105 5.95778L18.6483 6.21091C18.7622 6.27701 18.8917 6.31163 19.0233 6.31122C19.1885 6.31128 19.3491 6.25683 19.4801 6.15632C19.6111 6.0558 19.7053 5.91484 19.7481 5.75531C19.7908 5.59578 19.7797 5.42661 19.7165 5.27404C19.6532 5.12147 19.5414 4.99404 19.3983 4.91153ZM16.7499 4.99872C16.6016 4.99872 16.4566 4.95473 16.3332 4.87232C16.2099 4.78991 16.1138 4.67278 16.057 4.53573C16.0002 4.39869 15.9854 4.24789 16.0143 4.1024C16.0433 3.95692 16.1147 3.82328 16.2196 3.71839C16.3245 3.6135 16.4581 3.54207 16.6036 3.51313C16.7491 3.48419 16.8999 3.49904 17.0369 3.55581C17.174 3.61257 17.2911 3.7087 17.3735 3.83204C17.4559 3.95538 17.4999 4.10038 17.4999 4.24872C17.4999 4.44763 17.4209 4.6384 17.2802 4.77905C17.1396 4.9197 16.9488 4.99872 16.7499 4.99872ZM9.9999 6.12372C10.8158 6.12372 11.6133 6.36565 12.2916 6.81891C12.97 7.27217 13.4987 7.9164 13.8109 8.67015C14.1231 9.42389 14.2048 10.2533 14.0456 11.0535C13.8865 11.8536 13.4936 12.5886 12.9167 13.1655C12.3398 13.7424 11.6048 14.1353 10.8046 14.2945C10.0045 14.4536 9.17508 14.3719 8.42133 14.0597C7.66759 13.7475 7.02335 13.2188 6.57009 12.5404C6.11683 11.8621 5.8749 11.0646 5.8749 10.2487C5.8749 9.1547 6.3095 8.10549 7.08309 7.3319C7.85667 6.55832 8.90588 6.12372 9.9999 6.12372ZM19.6149 9.37497C19.9575 11.4117 19.644 13.5047 18.7196 15.3517C17.7952 17.1987 16.3077 18.7041 14.472 19.6505C12.6362 20.597 10.5471 20.9356 8.50641 20.6174C6.46567 20.2992 4.57887 19.3407 3.11842 17.8802C1.65796 16.4198 0.699432 14.5329 0.381224 12.4922C0.0630159 10.4515 0.401595 8.36242 1.34808 6.52665C2.29456 4.69089 3.79996 3.20342 5.64693 2.27901C7.49391 1.35459 9.58688 1.04108 11.6237 1.38372C11.8183 1.41815 11.9916 1.52806 12.1056 1.68954C12.2197 1.85102 12.2654 2.05102 12.2328 2.24601C12.2001 2.44101 12.0918 2.61524 11.9314 2.7308C11.771 2.84636 11.5714 2.89391 11.3762 2.86309C10.1932 2.66409 8.98099 2.72526 7.82404 3.04235C6.66708 3.35944 5.59312 3.92484 4.67689 4.69919C3.76066 5.47354 3.02418 6.43824 2.51869 7.52617C2.0132 8.61409 1.75085 9.7991 1.7499 10.9987C1.74812 13.0183 2.49039 14.9677 3.8349 16.4747C4.38017 15.6838 5.07281 15.0055 5.8749 14.4768C5.94329 14.4317 6.02459 14.4102 6.10636 14.4156C6.18814 14.421 6.26589 14.4531 6.32771 14.5068C7.34671 15.3885 8.64913 15.8738 9.99662 15.8738C11.3441 15.8738 12.6465 15.3885 13.6655 14.5068C13.7274 14.4528 13.8054 14.4206 13.8873 14.4152C13.9693 14.4098 14.0508 14.4315 14.1193 14.4768C14.9222 15.0055 15.6157 15.6838 16.1621 16.4747C17.5076 14.9681 18.2509 13.0186 18.2499 10.9987C18.2499 10.5376 18.2117 10.0773 18.1355 9.62247C18.1183 9.52491 18.1205 9.42489 18.1421 9.3282C18.1638 9.23151 18.2043 9.14007 18.2615 9.05914C18.3186 8.97822 18.3913 8.90943 18.4752 8.85673C18.5591 8.80404 18.6526 8.76849 18.7503 8.75214C18.848 8.73579 18.948 8.73897 19.0445 8.76148C19.141 8.784 19.2321 8.82541 19.3124 8.88332C19.3928 8.94123 19.461 9.01449 19.5129 9.09888C19.5648 9.18327 19.5995 9.2771 19.6149 9.37497Z" />
  </svg>
);
export const SettingsIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M10 5.50004C9.11003 5.50004 8.24 5.76396 7.49998 6.25843C6.75996 6.7529 6.18318 7.4557 5.84259 8.27797C5.50199 9.10024 5.41288 10.005 5.58651 10.8779C5.76014 11.7509 6.18873 12.5527 6.81806 13.182C7.4474 13.8114 8.24922 14.2399 9.12214 14.4136C9.99505 14.5872 10.8999 14.4981 11.7221 14.1575C12.5444 13.8169 13.2472 13.2401 13.7417 12.5001C14.2361 11.7601 14.5 10.8901 14.5 10C14.4988 8.80695 14.0243 7.66308 13.1807 6.81943C12.337 5.97579 11.1931 5.50128 10 5.50004ZM10 13C9.4067 13 8.82668 12.8241 8.33333 12.4945C7.83999 12.1648 7.45547 11.6963 7.2284 11.1481C7.00134 10.5999 6.94193 9.99672 7.05769 9.41477C7.17344 8.83283 7.45917 8.29828 7.87872 7.87872C8.29828 7.45917 8.83283 7.17344 9.41477 7.05769C9.99672 6.94193 10.5999 7.00134 11.1481 7.2284C11.6963 7.45547 12.1648 7.83999 12.4945 8.33333C12.8241 8.82668 13 9.4067 13 10C13 10.7957 12.684 11.5588 12.1214 12.1214C11.5588 12.684 10.7957 13 10 13ZM18.25 10.2025C18.2538 10.0675 18.2538 9.93254 18.25 9.79754L19.6488 8.05004C19.7221 7.95829 19.7729 7.8506 19.797 7.73563C19.8211 7.62067 19.8179 7.50165 19.7875 7.38817C19.5582 6.52623 19.2152 5.69865 18.7675 4.92723C18.7089 4.82628 18.6276 4.7404 18.5299 4.67644C18.4322 4.61248 18.321 4.57219 18.205 4.55879L15.9813 4.31129C15.8888 4.21379 15.795 4.12004 15.7 4.03004L15.4375 1.80067C15.424 1.68462 15.3836 1.57333 15.3195 1.47567C15.2554 1.37801 15.1693 1.29668 15.0682 1.23817C14.2965 0.791306 13.4689 0.448637 12.6072 0.219105C12.4937 0.188915 12.3746 0.185827 12.2597 0.210091C12.1447 0.234354 12.037 0.28529 11.9454 0.358793L10.2025 1.75004C10.0675 1.75004 9.93254 1.75004 9.79754 1.75004L8.05004 0.354105C7.95829 0.280763 7.8506 0.229997 7.73563 0.205897C7.62067 0.181797 7.50165 0.185037 7.38817 0.215355C6.52637 0.445068 5.69884 0.788058 4.92723 1.23536C4.82628 1.29397 4.7404 1.37535 4.67644 1.473C4.61248 1.57065 4.57219 1.68189 4.55879 1.79786L4.31129 4.02536C4.21379 4.11848 4.12004 4.21223 4.03004 4.30661L1.80067 4.56254C1.68462 4.57605 1.57333 4.61647 1.47567 4.6806C1.37801 4.74473 1.29668 4.83079 1.23817 4.93192C0.791306 5.70363 0.448637 6.53114 0.219105 7.39286C0.188915 7.50641 0.185827 7.62547 0.210091 7.74044C0.234354 7.8554 0.28529 7.96306 0.358793 8.05473L1.75004 9.79754C1.75004 9.93254 1.75004 10.0675 1.75004 10.2025L0.354105 11.95C0.280763 12.0418 0.229997 12.1495 0.205897 12.2645C0.181797 12.3794 0.185037 12.4984 0.215355 12.6119C0.444658 13.4739 0.78767 14.3014 1.23536 15.0729C1.29397 15.1738 1.37535 15.2597 1.473 15.3236C1.57065 15.3876 1.68189 15.4279 1.79786 15.4413L4.02161 15.6888C4.11473 15.7863 4.20848 15.88 4.30286 15.97L4.56254 18.1994C4.57605 18.3155 4.61647 18.4268 4.6806 18.5244C4.74473 18.6221 4.83079 18.7034 4.93192 18.7619C5.70363 19.2088 6.53114 19.5515 7.39286 19.781C7.50641 19.8112 7.62547 19.8143 7.74044 19.79C7.8554 19.7657 7.96306 19.7148 8.05473 19.6413L9.79754 18.25C9.93254 18.2538 10.0675 18.2538 10.2025 18.25L11.95 19.6488C12.0418 19.7221 12.1495 19.7729 12.2645 19.797C12.3794 19.8211 12.4984 19.8179 12.6119 19.7875C13.4739 19.5582 14.3014 19.2152 15.0729 18.7675C15.1738 18.7089 15.2597 18.6276 15.3236 18.5299C15.3876 18.4322 15.4279 18.321 15.4413 18.205L15.6888 15.9813C15.7863 15.8888 15.88 15.795 15.97 15.7L18.1994 15.4375C18.3155 15.424 18.4268 15.3836 18.5244 15.3195C18.6221 15.2554 18.7034 15.1693 18.7619 15.0682C19.2088 14.2965 19.5515 13.4689 19.781 12.6072C19.8112 12.4937 19.8143 12.3746 19.79 12.2597C19.7657 12.1447 19.7148 12.037 19.6413 11.9454L18.25 10.2025ZM16.7407 9.59317C16.7566 9.86419 16.7566 10.1359 16.7407 10.4069C16.7295 10.5925 16.7876 10.7756 16.9038 10.9207L18.2341 12.5829C18.0815 13.068 17.886 13.5386 17.65 13.9891L15.5313 14.2291C15.3468 14.2496 15.1764 14.3378 15.0532 14.4766C14.8727 14.6796 14.6805 14.8718 14.4775 15.0522C14.3387 15.1755 14.2505 15.3458 14.23 15.5304L13.9947 17.6472C13.5443 17.8833 13.0736 18.0788 12.5885 18.2313L10.9254 16.901C10.7923 16.7946 10.627 16.7368 10.4566 16.7369H10.4116C10.1406 16.7529 9.86887 16.7529 9.59786 16.7369C9.41231 16.7257 9.22922 16.7839 9.08411 16.9L7.41723 18.2313C6.93211 18.0786 6.4615 17.8832 6.01098 17.6472L5.77098 15.5313C5.7505 15.3468 5.66231 15.1764 5.52348 15.0532C5.32052 14.8727 5.12832 14.6805 4.94786 14.4775C4.82461 14.3387 4.65424 14.2505 4.46973 14.23L2.35286 13.9938C2.11679 13.5433 1.92132 13.0727 1.76879 12.5875L3.09911 10.9244C3.21527 10.7793 3.2734 10.5962 3.26223 10.4107C3.24629 10.1397 3.24629 9.86793 3.26223 9.59692C3.2734 9.41137 3.21527 9.22829 3.09911 9.08317L1.76879 7.41723C1.92144 6.93211 2.1169 6.4615 2.35286 6.01098L4.46879 5.77098C4.65331 5.7505 4.82367 5.66231 4.94692 5.52348C5.12738 5.32052 5.31958 5.12832 5.52254 4.94786C5.66193 4.82452 5.75048 4.65377 5.77098 4.46879L6.00629 2.35286C6.45677 2.11679 6.92738 1.92132 7.41254 1.76879L9.07567 3.09911C9.22079 3.21527 9.40387 3.2734 9.58942 3.26223C9.86043 3.24629 10.1322 3.24629 10.4032 3.26223C10.5887 3.2734 10.7718 3.21527 10.9169 3.09911L12.5829 1.76879C13.068 1.92144 13.5386 2.1169 13.9891 2.35286L14.2291 4.46879C14.2496 4.65331 14.3378 4.82367 14.4766 4.94692C14.6796 5.12738 14.8718 5.31958 15.0522 5.52254C15.1755 5.66138 15.3458 5.74956 15.5304 5.77004L17.6472 6.00536C17.8833 6.45583 18.0788 6.92644 18.2313 7.41161L16.901 9.07473C16.7837 9.22107 16.7255 9.40604 16.7379 9.59317H16.7407Z" />
  </svg>
);
export const SettingsIconActive = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M18.25 10.2025C18.2537 10.0675 18.2537 9.93251 18.25 9.79751L19.6487 8.05001C19.7221 7.95826 19.7729 7.85056 19.797 7.7356C19.8211 7.62064 19.8178 7.50162 19.7875 7.38814C19.5578 6.52634 19.2148 5.69881 18.7675 4.9272C18.7089 4.82625 18.6275 4.74037 18.5299 4.67641C18.4322 4.61245 18.321 4.57216 18.205 4.55876L15.9812 4.31126C15.8887 4.21376 15.795 4.12001 15.7 4.03001L15.4375 1.80064C15.424 1.68459 15.3836 1.57329 15.3194 1.47563C15.2553 1.37797 15.1693 1.29665 15.0681 1.23814C14.2966 0.791183 13.469 0.448808 12.6072 0.220012C12.4937 0.189694 12.3747 0.186454 12.2597 0.210554C12.1448 0.234654 12.0371 0.28542 11.9453 0.358762L10.2025 1.75001C10.0675 1.75001 9.9325 1.75001 9.7975 1.75001L8.05 0.354075C7.95825 0.280733 7.85055 0.229967 7.73559 0.205867C7.62062 0.181766 7.50161 0.185006 7.38812 0.215325C6.52633 0.445038 5.6988 0.788028 4.92719 1.23532C4.82623 1.29394 4.74036 1.37532 4.67639 1.47297C4.61243 1.57062 4.57215 1.68186 4.55875 1.79782L4.31125 4.02532C4.21375 4.11845 4.12 4.2122 4.03 4.30657L1.80062 4.56251C1.68457 4.57601 1.57328 4.61644 1.47562 4.68057C1.37796 4.7447 1.29663 4.83076 1.23812 4.93189C0.79126 5.7036 0.448592 6.53111 0.21906 7.39283C0.188869 7.50638 0.185781 7.62544 0.210045 7.74041C0.234308 7.85537 0.285244 7.96303 0.358747 8.0547L1.75 9.79751C1.75 9.93251 1.75 10.0675 1.75 10.2025L0.35406 11.95C0.280717 12.0418 0.229951 12.1495 0.205851 12.2644C0.181751 12.3794 0.184991 12.4984 0.215309 12.6119C0.445022 13.4737 0.788013 14.3012 1.23531 15.0728C1.29393 15.1738 1.3753 15.2597 1.47296 15.3236C1.57061 15.3876 1.68184 15.4279 1.79781 15.4413L4.02156 15.6888C4.11469 15.7863 4.20844 15.88 4.30281 15.97L4.5625 18.1994C4.576 18.3154 4.61643 18.4267 4.68056 18.5244C4.74469 18.6221 4.83075 18.7034 4.93187 18.7619C5.70359 19.2088 6.5311 19.5514 7.39281 19.781C7.50636 19.8111 7.62542 19.8142 7.74039 19.79C7.85536 19.7657 7.96302 19.7148 8.05469 19.6413L9.7975 18.25C9.9325 18.2538 10.0675 18.2538 10.2025 18.25L11.95 19.6488C12.0417 19.7221 12.1494 19.7729 12.2644 19.797C12.3794 19.8211 12.4984 19.8178 12.6119 19.7875C13.4738 19.5582 14.3014 19.2152 15.0728 18.7675C15.1738 18.7089 15.2596 18.6275 15.3236 18.5299C15.3876 18.4322 15.4278 18.321 15.4412 18.205L15.6887 15.9813C15.7862 15.8888 15.88 15.795 15.97 15.7L18.1994 15.4375C18.3154 15.424 18.4267 15.3836 18.5244 15.3195C18.622 15.2553 18.7034 15.1693 18.7619 15.0681C19.2087 14.2964 19.5514 13.4689 19.7809 12.6072C19.8111 12.4936 19.8142 12.3746 19.79 12.2596C19.7657 12.1447 19.7148 12.037 19.6412 11.9453L18.25 10.2025ZM10 13.75C9.25832 13.75 8.53329 13.5301 7.91661 13.118C7.29992 12.706 6.81928 12.1203 6.53545 11.4351C6.25162 10.7499 6.17736 9.99585 6.32205 9.26842C6.46675 8.541 6.8239 7.87281 7.34835 7.34836C7.87279 6.82392 8.54098 6.46676 9.26841 6.32207C9.99584 6.17737 10.7498 6.25164 11.4351 6.53546C12.1203 6.81929 12.706 7.29994 13.118 7.91662C13.5301 8.53331 13.75 9.25833 13.75 10C13.75 10.9946 13.3549 11.9484 12.6516 12.6517C11.9484 13.3549 10.9946 13.75 10 13.75Z" />
  </svg>
);

export const ChevronDownIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4 6L8 10L12 6"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
);

export const ChevronUpIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 10L8 6L4 10"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
);

export const ChevronLeftIcon = ({ className }: { className?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M10 12L6 8L10 4"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
);

export const ChevronRightIcon = ({ className }: { className?: string }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M6 4L10 8L6 12"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
      fill="none"
    />
  </svg>
);

export const ToggleIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.43945 11.9998L12.219 6.21876L13.281 7.27926L8.56045 11.9998L13.281 16.7188L12.219 17.7808L6.43945 11.9998ZM10.9395 11.9998L16.719 6.21876L17.781 7.27926L13.0605 11.9998L17.781 16.7188L16.7205 17.7808L10.9395 11.9998Z"
      fill="#448600"
    />
  </svg>
);

export const PlusIcon = ({
  width,
  height,
}: {
  width?: number;
  height?: number;
}) => (
  <svg
    width={width ?? "16"}
    height={height ?? "16"}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.6663 8.66537H8.66634V12.6654H7.33301V8.66537H3.33301V7.33203H7.33301V3.33203H8.66634V7.33203H12.6663V8.66537Z"
      fill="#448600"
    />
  </svg>
);

export const VerticalElipsisIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.00035 3.9465C8.17009 3.9465 8.33288 3.87908 8.4529 3.75905C8.57292 3.63903 8.64035 3.47624 8.64035 3.3065C8.64035 3.13677 8.57292 2.97398 8.4529 2.85396C8.33288 2.73393 8.17009 2.6665 8.00035 2.6665C7.83061 2.6665 7.66783 2.73393 7.5478 2.85396C7.42778 2.97398 7.36035 3.13677 7.36035 3.3065C7.36035 3.47624 7.42778 3.63903 7.5478 3.75905C7.66783 3.87908 7.83061 3.9465 8.00035 3.9465ZM8.00035 8.63984C8.17009 8.63984 8.33288 8.57241 8.4529 8.45239C8.57292 8.33236 8.64035 8.16958 8.64035 7.99984C8.64035 7.8301 8.57292 7.66731 8.4529 7.54729C8.33288 7.42727 8.17009 7.35984 8.00035 7.35984C7.83061 7.35984 7.66783 7.42727 7.5478 7.54729C7.42778 7.66731 7.36035 7.8301 7.36035 7.99984C7.36035 8.16958 7.42778 8.33236 7.5478 8.45239C7.66783 8.57241 7.83061 8.63984 8.00035 8.63984ZM8.00035 13.3332C8.17009 13.3332 8.33288 13.2657 8.4529 13.1457C8.57292 13.0257 8.64035 12.8629 8.64035 12.6932C8.64035 12.5234 8.57292 12.3606 8.4529 12.2406C8.33288 12.1206 8.17009 12.0532 8.00035 12.0532C7.83061 12.0532 7.66783 12.1206 7.5478 12.2406C7.42778 12.3606 7.36035 12.5234 7.36035 12.6932C7.36035 12.8629 7.42778 13.0257 7.5478 13.1457C7.66783 13.2657 7.83061 13.3332 8.00035 13.3332Z"
      stroke="black"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

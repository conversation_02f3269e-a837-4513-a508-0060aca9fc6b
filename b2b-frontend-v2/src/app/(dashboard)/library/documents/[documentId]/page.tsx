"use client";

import React, { useState, useEffect, use } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import DashboardHeader from "@/components/DashboardHeader";
import FolderSidebar from "@/components/FolderSidebar";
import DocumentViewer from "./components/DocumentViewer";
import { DocumentI } from "@/components/DocumentCard/types";
import { FolderI } from "@/components/FolderSidebar/types";

// Dummy data
const folders: FolderI[] = [
  { id: "1", name: "Folder 1", count: 5 },
  { id: "2", name: "Folder 2", count: 3 },
  { id: "3", name: "Folder 3", count: 8 },
  { id: "4", name: "Folder 4", count: 2 },
  { id: "5", name: "Folder 5", count: 12 },
  { id: "6", name: "Folder 6", count: 7 },
];

const documents: DocumentI[] = [
  {
    id: "1",
    name: "Seaspan Shipping Company Profile",
    type: "pdf",
    folderId: "1",
    fileUrl: "https://www.learningcontainer.com/wp-content/uploads/2019/09/sample-pdf-file.pdf",
    size: 2048000,
    lastModified: "2024-01-15",
    version: "1.0.1",
  },
  {
    id: "2",
    name: "Maritime Infrastructure Report",
    type: "word",
    folderId: "1",
    fileUrl: "https://calibre-ebook.com/downloads/demos/demo.docx",
    size: 1024000,
    lastModified: "2024-01-14",
    version: "1.0.2",
  },
  {
    id: "3",
    name: "Container Leasing Agreement",
    type: "pdf",
    folderId: "1",
    fileUrl: "https://sample-videos.com/zip/10/pdf/mp4/SamplePDFFile_30mb.pdf",
    size: 3072000,
    lastModified: "2024-01-13",
    version: "1.0.3",
  },
  {
    id: "4",
    name: "Fleet Operations Manual",
    type: "word",
    folderId: "1",
    fileUrl: "https://filesamples.com/samples/document/docx/sample3.docx",
    size: 4096000,
    lastModified: "2024-01-12",
    version: "1.1.2",
  },
  {
    id: "5",
    name: "Safety Protocols Document",
    type: "pdf",
    folderId: "1",
    fileUrl: "https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf",
    size: 1536000,
    lastModified: "2024-01-11",
    version: "1.2.3",
  },
  {
    id: "6",
    name: "Financial Report Q4",
    type: "word",
    folderId: "2",
    fileUrl: "https://www.soundczech.cz/temp/lorem-ipsum.docx",
    size: 2560000,
    lastModified: "2024-01-10",
    version: "1.0.1",
  },
  {
    id: "7",
    name: "Environmental Compliance",
    type: "pdf",
    folderId: "3",
    fileUrl: "https://www.orimi.com/pdf-test.pdf",
    size: 1792000,
    lastModified: "2024-01-09",
    version: "2.0.1",
  },
  {
    id: "8",
    name: "Vessel Inspection Report",
    type: "pdf",
    folderId: "1",
    fileUrl: "https://www.africau.edu/images/default/sample.pdf",
    size: 2304000,
    lastModified: "2024-01-08",
    version: "1.3.1",
  },
  {
    id: "9",
    name: "Crew Training Manual",
    type: "word",
    folderId: "2",
    fileUrl: "https://scholar.harvard.edu/files/torman_personal/files/sampledocument.docx",
    size: 1843200,
    lastModified: "2024-01-07",
    version: "2.1.0",
  },
  {
    id: "10",
    name: "Port Authority Guidelines",
    type: "pdf",
    folderId: "3",
    fileUrl: "https://www.tutorialspoint.com/html/sample.pdf",
    size: 2867200,
    lastModified: "2024-01-06",
    version: "1.4.2",
  },
];

type DocumentViewerPagePropsI = {
  params: { documentId: string };
};

const DocumentViewerPage: React.FC<DocumentViewerPagePropsI> = ({ params }) => {
  const resolvedParams = params instanceof Promise ? use(params) : params;
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedFolder, setSelectedFolder] = useState<string | null>(
    searchParams.get("folder") || "Folder 1"
  );
  const [currentDocument, setCurrentDocument] = useState<DocumentI | null>(null);
  const [folderDocuments, setFolderDocuments] = useState<DocumentI[]>([]);

  useEffect(() => {
    // Find current document
    const document = documents.find((doc) => doc.id === resolvedParams.documentId);
    setCurrentDocument(document || null);

    // Get documents in the selected folder
    const selectedFolderObj = folders.find((f) => f.name === selectedFolder);
    if (selectedFolderObj) {
      const docsInFolder = documents.filter(
        (doc) => doc.folderId === selectedFolderObj.id
      );
      setFolderDocuments(docsInFolder);
    }
  }, [resolvedParams.documentId, selectedFolder]);

  const handleBackToDocuments = () => {
    const backUrl = selectedFolder
      ? `/library/documents?folder=${encodeURIComponent(selectedFolder)}`
      : "/library/documents";
    router.push(backUrl);
  };

  const handleFolderSelect = (folderName: string | null) => {
    setSelectedFolder(folderName);
    const newParams = new URLSearchParams(searchParams.toString());
    if (folderName) {
      newParams.set("folder", folderName);
    } else {
      newParams.delete("folder");
    }
    router.push(`?${newParams.toString()}`);
  };

  const handleNavigateToDocument = (documentId: string) => {
    const newParams = new URLSearchParams(searchParams.toString());
    const newUrl = selectedFolder
      ? `/library/documents/${documentId}?${newParams.toString()}`
      : `/library/documents/${documentId}`;
    router.push(newUrl);
  };

  const handleDownload = () => {
    if (currentDocument?.fileUrl) {
      window.open(currentDocument.fileUrl, "_blank");
    }
  };

  if (!currentDocument) {
    return (
      <div className="flex flex-1 flex-col">
        <DashboardHeader
          title="Document Not Found"
          description="The requested document could not be found"
          actions={[
            {
              label: "Back to Documents",
              variant: "primary",
              className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
              onClick: handleBackToDocuments,
            },
          ]}
        />
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-neutral-900 mb-2">
              Document Not Found
            </h2>
            <p className="text-neutral-600 mb-4">
              The document you're looking for doesn't exist or has been moved.
            </p>
            <button
              onClick={handleBackToDocuments}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
            >
              Back to Documents
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title={currentDocument.name}
        description="Document Viewer"
        actions={[
          {
            label: "Download",
            variant: "ghost",
            className:
              "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto",
            onClick: handleDownload,
          },
          {
            label: "Back to Documents",
            variant: "primary",
            className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
            onClick: handleBackToDocuments,
          },
        ]}
      />

      <div className="flex flex-1 bg-neutral-50">
        <FolderSidebar
          folders={folders}
          selectedFolder={selectedFolder}
          onFolderSelect={handleFolderSelect}
        />

        <div className="flex-1 p-6">
          <DocumentViewer
            document={currentDocument}
            folderDocuments={folderDocuments}
            onNavigateToDocument={handleNavigateToDocument}
          />
        </div>
      </div>
    </div>
  );
};

export default DocumentViewerPage;

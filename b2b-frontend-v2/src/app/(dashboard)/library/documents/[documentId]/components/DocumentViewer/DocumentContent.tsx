import React, { useState } from "react";
import { DocumentI } from "@/components/DocumentCard/types";
import UnsupportedFileError from "./UnsupportedFileError";
import WordViewer from "./WordViewer";
import PDFViewer from "./PDFViewer";


type DocumentContentPropsI = {
  document: DocumentI;
};

const DocumentContent: React.FC<DocumentContentPropsI> = ({ document }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const renderDocumentViewer = () => {
    if (!document.fileUrl) {
      return (
        <UnsupportedFileError
          message="No file URL available for this document"
          onDownload={() => {}}
        />
      );
    }

    switch (document.type.toLowerCase()) {
      case "pdf":
        return (
          <PDFViewer
            fileUrl={document.fileUrl}
            onLoadStart={() => setLoading(true)}
            onLoadSuccess={() => setLoading(false)}
            onLoadError={(error: React.SetStateAction<string | null>) => {
              setLoading(false);
              setError(error);
            }}
          />
        );
      
      case "word":
      case "docx":
        return (
          <WordViewer
            fileUrl={document.fileUrl}
            onLoadStart={() => setLoading(true)}
            onLoadSuccess={() => setLoading(false)}
            onLoadError={(error) => {
              setLoading(false);
              setError(error);
            }}
          />
        );
      
      default:
        return (
          <UnsupportedFileError
            message={`${document.type.toUpperCase()} files are not supported for viewing`}
            onDownload={() => {
              if (document.fileUrl) {
                window.open(document.fileUrl, "_blank");
              }
            }}
          />
        );
    }
  };

  if (error) {
    return (
      <UnsupportedFileError
        message={error}
        onDownload={() => {
          if (document.fileUrl) {
            window.open(document.fileUrl, "_blank");
          }
        }}
      />
    );
  }

  return (
    <div className="relative w-full h-full">
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
          <div className="flex flex-col items-center gap-4">
            <div className="w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
            <p className="text-sm text-neutral-600">Loading document...</p>
          </div>
        </div>
      )}
      
      <div className="w-full h-full">
        {renderDocumentViewer()}
      </div>
    </div>
  );
};

export default DocumentContent;

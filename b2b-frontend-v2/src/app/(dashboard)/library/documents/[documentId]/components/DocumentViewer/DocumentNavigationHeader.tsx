import React from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@/assets/images/svgs/common";
import DocumentVersionDropdown from "./DocumentVersionDropdown";

type DocumentNavigationHeaderPropsI = {
  currentIndex: number;
  totalDocuments: number;
  hasPrevious: boolean;
  hasNext: boolean;
  onPrevious: () => void;
  onNext: () => void;
  onFirst: () => void;
  onLast: () => void;
  selectedVersion: string;
  versions: string[];
  onVersionChange: (version: string) => void;
};

const DocumentNavigationHeader: React.FC<DocumentNavigationHeaderPropsI> = ({
  currentIndex,
  totalDocuments,
  hasPrevious,
  hasNext,
  onPrevious,
  onNext,
  onFirst,
  onLast,
  selectedVersion,
  versions,
  onVersionChange,
}) => {
  return (
    <div className="flex items-center justify-between bg-white rounded-lg border p-4">
      {/* Left side - Navigation controls */}
      <div className="flex items-center gap-2">
        {/* First document */}
        <button
          onClick={onFirst}
          disabled={currentIndex === 0}
          className="p-2 rounded-md border border-neutral-200 hover:bg-neutral-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="First document"
        >
          <div className="flex items-center">
            <ChevronLeftIcon className="w-4 h-4" />
            <ChevronLeftIcon className="w-4 h-4 -ml-2" />
          </div>
        </button>

        {/* Previous document */}
        <button
          onClick={onPrevious}
          disabled={!hasPrevious}
          className="p-2 rounded-md border border-neutral-200 hover:bg-neutral-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Previous document"
        >
          <ChevronLeftIcon className="w-4 h-4" />
        </button>

        {/* Document counter */}
        <div className="px-3 py-2 bg-neutral-50 rounded-md border border-neutral-200">
          <span className="text-sm font-medium text-neutral-700">
            {currentIndex + 1}/{totalDocuments}
          </span>
        </div>

        {/* Next document */}
        <button
          onClick={onNext}
          disabled={!hasNext}
          className="p-2 rounded-md border border-neutral-200 hover:bg-neutral-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Next document"
        >
          <ChevronRightIcon className="w-4 h-4" />
        </button>

        {/* Last document */}
        <button
          onClick={onLast}
          disabled={currentIndex === totalDocuments - 1}
          className="p-2 rounded-md border border-neutral-200 hover:bg-neutral-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Last document"
        >
          <div className="flex items-center">
            <ChevronRightIcon className="w-4 h-4" />
            <ChevronRightIcon className="w-4 h-4 -ml-2" />
          </div>
        </button>
      </div>

      {/* Right side - Version dropdown */}
      <div className="flex items-center">
        <DocumentVersionDropdown
          selectedVersion={selectedVersion}
          versions={versions}
          onVersionChange={onVersionChange}
        />
      </div>
    </div>
  );
};

export default DocumentNavigationHeader;

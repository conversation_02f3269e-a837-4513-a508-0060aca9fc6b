import React from "react";

type UnsupportedFileErrorPropsI = {
  message: string;
  onDownload: () => void;
};

const UnsupportedFileError: React.FC<UnsupportedFileErrorPropsI> = ({
  message,
  onDownload,
}) => {
  return (
    <div className="w-full h-full flex items-center justify-center bg-neutral-50">
      <div className="text-center max-w-md px-6">
        {/* Error Icon */}
        <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
          <svg
            className="w-8 h-8 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>

        {/* Error Message */}
        <h3 className="text-lg font-semibold text-neutral-900 mb-2">
          Unable to Display Document
        </h3>
        <p className="text-sm text-neutral-600 mb-6">
          {message}
        </p>

        {/* Download Button */}
        <button
          onClick={onDownload}
          className="inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Download Document
        </button>

        {/* Additional Info */}
        <p className="text-xs text-neutral-500 mt-4">
          You can download the document to view it in your preferred application.
        </p>
      </div>
    </div>
  );
};

export default UnsupportedFileError;

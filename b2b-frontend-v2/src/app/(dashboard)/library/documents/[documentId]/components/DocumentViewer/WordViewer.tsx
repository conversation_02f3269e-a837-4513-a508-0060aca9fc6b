import React, { useEffect, useState } from "react";

type WordViewerPropsI = {
  fileUrl: string;
  onLoadStart: () => void;
  onLoadSuccess: () => void;
  onLoadError: (error: string) => void;
};

const WordViewer: React.FC<WordViewerPropsI> = ({
  fileUrl,
  onLoadStart,
  onLoadSuccess,
  onLoadError,
}) => {
  const [content, setContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    onLoadStart();
    loadWordDocument();
  }, [fileUrl, onLoadStart]);

  const loadWordDocument = async () => {
    try {
      setIsLoading(true);
      
      // For now, we'll show a placeholder content
      // In production, you would use mammoth.js to convert .docx to HTML
      setTimeout(() => {
        const mockContent = `
          <div style="font-family: 'Times New Roman', serif; line-height: 1.6; padding: 40px; max-width: 800px; margin: 0 auto;">
            <h1 style="color: #2c3e50; margin-bottom: 20px;">Seaspan Shipping Company</h1>
            <p style="color: #7f8c8d; font-size: 18px; margin-bottom: 30px;">A Global Leader in Containership Leasing and Maritime Infrastructure</p>
            
            <p style="margin-bottom: 20px;">Seaspan Shipping Company is a leading player in the global maritime logistics industry. As one of the world's largest independent containership owners and operators in the world, Seaspan plays a foundational role in enabling global commerce. With its headquarters in Hong Kong and operational reach across continents, Seaspan serves major shipping lines by providing long-term, fixed-rate charters on modern, eco-efficient vessels.</p>
            
            <h2 style="color: #34495e; margin-top: 30px; margin-bottom: 15px;">Key Highlights</h2>
            <ul style="margin-bottom: 20px;">
              <li style="margin-bottom: 8px;"><strong>🌍 Global Presence:</strong> Operates across major international trade routes.</li>
              <li style="margin-bottom: 8px;"><strong>🚢 Large Fleet:</strong> Over 140 vessels and growing, with capacity exceeding 1.4 million TEU.</li>
              <li style="margin-bottom: 8px;"><strong>♻️ Sustainability Focus:</strong> Investing in LNG, methanol-ready vessels, and alternative fuels.</li>
              <li style="margin-bottom: 8px;"><strong>📈 Financial Stability:</strong> Long-term charters ensure predictable revenue and resilience.</li>
              <li style="margin-bottom: 8px;"><strong>🤝 Strategic Partnerships:</strong> Works with top global carriers like COSCO, Maersk, MSC, and Hapag-Lloyd.</li>
            </ul>
            
            <h2 style="color: #34495e; margin-top: 30px; margin-bottom: 15px;">1. Fleet and Operational Strength</h2>
            <p style="margin-bottom: 20px;">Seaspan's operations are anchored in the strength, scale, and modernity of its fleet. The company prioritizes safety, efficiency, and uptime through continuous fleet upgrades and smart asset management.</p>
            
            <h3 style="color: #2c3e50; margin-top: 25px; margin-bottom: 10px;">Fleet Details:</h3>
            <ul style="margin-bottom: 20px;">
              <li style="margin-bottom: 8px;">More than 140 containerships of varying sizes, from feeder to ultra-large vessels.</li>
              <li style="margin-bottom: 8px;">Over 1.4 million TEU in total container carrying capacity.</li>
              <li style="margin-bottom: 8px;">Ships equipped with cutting-edge emissions control systems.</li>
            </ul>
          </div>
        `;
        
        setContent(mockContent);
        setIsLoading(false);
        onLoadSuccess();
      }, 1500);
      
    } catch (error) {
      setIsLoading(false);
      onLoadError("Failed to load Word document");
    }
  };

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-neutral-50">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-sm text-neutral-600">Converting Word document...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full overflow-auto bg-white">
      <div 
        className="min-h-full"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </div>
  );
};

export default WordViewer;

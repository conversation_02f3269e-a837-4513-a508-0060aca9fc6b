import React, { useState } from "react";
import { DocumentI } from "@/components/DocumentCard/types";
import DocumentNavigationHeader from "./DocumentNavigationHeader";
import DocumentContent from "./DocumentContent";
import DocumentVersionDropdown from "./DocumentVersionDropdown";

type DocumentViewerPropsI = {
  document: DocumentI;
  folderDocuments: DocumentI[];
  onNavigateToDocument: (documentId: string) => void;
};

const DocumentViewer: React.FC<DocumentViewerPropsI> = ({
  document,
  folderDocuments,
  onNavigateToDocument,
}) => {
  const [selectedVersion, setSelectedVersion] = useState(document.version || "1.0.1");

  // Find current document index for navigation
  const currentIndex = folderDocuments.findIndex((doc) => doc.id === document.id);
  const hasPrevious = currentIndex > 0;
  const hasNext = currentIndex < folderDocuments.length - 1;

  const handlePrevious = () => {
    if (hasPrevious) {
      onNavigateToDocument(folderDocuments[currentIndex - 1].id);
    }
  };

  const handleNext = () => {
    if (hasNext) {
      onNavigateToDocument(folderDocuments[currentIndex + 1].id);
    }
  };

  const handleFirstDocument = () => {
    if (folderDocuments.length > 0) {
      onNavigateToDocument(folderDocuments[0].id);
    }
  };

  const handleLastDocument = () => {
    if (folderDocuments.length > 0) {
      onNavigateToDocument(folderDocuments[folderDocuments.length - 1].id);
    }
  };

  // Mock version data
  const versions = [
    "Version 1.0.1",
    "Version 1.0.2", 
    "Version 1.0.3",
    "Version 1.1.2",
    "Version 1.2.3",
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Navigation Header */}
      <DocumentNavigationHeader
        currentIndex={currentIndex}
        totalDocuments={folderDocuments.length}
        hasPrevious={hasPrevious}
        hasNext={hasNext}
        onPrevious={handlePrevious}
        onNext={handleNext}
        onFirst={handleFirstDocument}
        onLast={handleLastDocument}
        selectedVersion={selectedVersion}
        versions={versions}
        onVersionChange={setSelectedVersion}
      />

      {/* Document Content */}
      <div className="flex-1 bg-white rounded-lg border overflow-hidden mt-4">
        <DocumentContent document={document} />
      </div>
    </div>
  );
};

export default DocumentViewer;

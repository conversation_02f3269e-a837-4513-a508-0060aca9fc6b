import React, { useState, useRef, useEffect } from "react";
import { ChevronDownIcon } from "@/assets/images/svgs/common";

type DocumentVersionDropdownPropsI = {
  selectedVersion: string;
  versions: string[];
  onVersionChange: (version: string) => void;
};

const DocumentVersionDropdown: React.FC<DocumentVersionDropdownPropsI> = ({
  selectedVersion,
  versions,
  onVersionChange,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleVersionSelect = (version: string) => {
    onVersionChange(version);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown trigger */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 bg-white border border-neutral-200 rounded-md hover:bg-neutral-50 transition-colors min-w-[140px] justify-between"
      >
        <span className="text-sm font-medium text-neutral-700">
          {selectedVersion}
        </span>
        <ChevronDownIcon 
          className={`w-4 h-4 text-neutral-500 transition-transform ${
            isOpen ? "rotate-180" : ""
          }`} 
        />
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-neutral-200 rounded-md shadow-lg z-50">
          <div className="py-1">
            {versions.map((version, index) => (
              <button
                key={version}
                onClick={() => handleVersionSelect(version)}
                className={`w-full text-left px-3 py-2 text-sm hover:bg-neutral-50 transition-colors ${
                  version === selectedVersion
                    ? "bg-primary-50 text-primary-700 font-medium"
                    : "text-neutral-700"
                }`}
              >
                {version}
              </button>
            ))}
            
            {/* Version History link */}
            <div className="border-t border-neutral-200 mt-1 pt-1">
              <button
                onClick={() => {
                  setIsOpen(false);
                  // Handle version history click
                  console.log("Version history clicked");
                }}
                className="w-full text-left px-3 py-2 text-sm text-primary-600 hover:bg-primary-50 transition-colors font-medium"
              >
                Version History
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentVersionDropdown;

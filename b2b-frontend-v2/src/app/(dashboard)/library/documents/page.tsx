"use client";
import DashboardHeader from "@/components/DashboardHeader";
import DocumentGrid from "@/components/DocumentGrid";
import FolderSidebar from "@/components/FolderSidebar";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { DocumentI } from "@/components/DocumentCard/types";

const LibraryPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedFolder, setSelectedFolder] = useState<string | null>(
    searchParams.get("folder") || "Folder 1",
  );

  useEffect(() => {
    const folderFromUrl = searchParams.get("folder");
    if (folderFromUrl) {
      setSelectedFolder(folderFromUrl);
    }
  }, [searchParams]);

  const folders = [
    { id: "1", name: "Folder 1", count: 5 },
    { id: "2", name: "Folder 2", count: 3 },
    { id: "3", name: "Folder 3", count: 8 },
    { id: "4", name: "Folder 4", count: 2 },
    { id: "5", name: "Folder 5", count: 12 },
    { id: "6", name: "Folder 6", count: 7 },
  ];

  const documents = [
    {
      id: "1",
      name: "Seaspan Shipping Company Profile",
      type: "pdf",
      folderId: "1",
      fileUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
      size: 2048000,
      lastModified: "2024-01-15",
      version: "1.0.1",
    },
    {
      id: "2",
      name: "Maritime Infrastructure Report",
      type: "word",
      folderId: "1",
      fileUrl: "https://file-examples.com/storage/fe68c8a7c66c4d6c2b8c9c8/2017/10/file_example_DOC_10kB.doc",
      size: 1024000,
      lastModified: "2024-01-14",
      version: "1.0.2",
    },
    {
      id: "3",
      name: "Container Leasing Agreement",
      type: "pdf",
      folderId: "1",
      fileUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
      size: 3072000,
      lastModified: "2024-01-13",
      version: "1.0.3",
    },
    {
      id: "4",
      name: "Fleet Operations Manual",
      type: "word",
      folderId: "1",
      fileUrl: "https://file-examples.com/storage/fe68c8a7c66c4d6c2b8c9c8/2017/10/file_example_DOC_10kB.doc",
      size: 4096000,
      lastModified: "2024-01-12",
      version: "1.1.2",
    },
    {
      id: "5",
      name: "Safety Protocols Document",
      type: "pdf",
      folderId: "1",
      fileUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
      size: 1536000,
      lastModified: "2024-01-11",
      version: "1.2.3",
    },
    {
      id: "6",
      name: "Financial Report Q4",
      type: "word",
      folderId: "2",
      fileUrl: "https://file-examples.com/storage/fe68c8a7c66c4d6c2b8c9c8/2017/10/file_example_DOC_10kB.doc",
      size: 2560000,
      lastModified: "2024-01-10",
      version: "1.0.1",
    },
    {
      id: "7",
      name: "Environmental Compliance",
      type: "pdf",
      folderId: "3",
      fileUrl: "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
      size: 1792000,
      lastModified: "2024-01-09",
      version: "2.0.1",
    },
  ];

  const handleDocumentSelect = (document: DocumentI) => {
    // Navigate to document viewer
    const documentUrl = selectedFolder
      ? `/library/documents/${document.id}?folder=${encodeURIComponent(selectedFolder)}`
      : `/library/documents/${document.id}`;
    router.push(documentUrl);
  };

  const handleFolderSelect = (folderName: string | null) => {
    setSelectedFolder(folderName);
    const newParams = new URLSearchParams(searchParams.toString());
    if (folderName) {
      newParams.set("folder", folderName);
    } else {
      newParams.delete("folder");
    }
    router.push(`?${newParams.toString()}`);
  };

  return (
    <div className="flex flex-1 flex-col">
      <DashboardHeader
        title="Documents"
        description="Manage and organize your document collection"
        actions={[
          {
            label: "Edit Document",
            variant: "ghost",
            className:
              "!px-4 !py-2 !text-sm !font-medium !border-neutral-300 !text-neutral-700 hover:!bg-neutral-50 !w-auto",
            onClick: () => console.log("Edit Document"),
          },
          {
            label: "Upload Document",
            variant: "primary",
            className: "!px-4 !py-2 !text-sm !font-medium !text-white !w-auto",
            onClick: () => console.log("Upload Document"),
          },
        ]}
      />

      <div className="flex flex-1 bg-neutral-50">
        <FolderSidebar
          folders={folders}
          selectedFolder={selectedFolder}
          onFolderSelect={handleFolderSelect}
        />

        <div className="flex-1 p-6">
          <DocumentGrid
            documents={documents}
            selectedDocument={null}
            onDocumentSelect={handleDocumentSelect}
          />
        </div>
      </div>
    </div>
  );
};

export default LibraryPage;
